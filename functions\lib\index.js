"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateEmpruntStatus = exports.generateEmpruntLabels = exports.createEmprunt = exports.setUserRole = exports.onUserCreate = void 0;
const app_1 = require("firebase-admin/app");
if (!(0, app_1.getApps)().length) {
    (0, app_1.initializeApp)(); // credentials auto-gérées par l’émulateur
}
/* ── Exports des Cloud Functions ── */
var onUserCreate_1 = require("./auth/onUserCreate");
Object.defineProperty(exports, "onUserCreate", { enumerable: true, get: function () { return onUserCreate_1.onUserCreate; } });
var setUserRole_1 = require("./auth/setUserRole");
Object.defineProperty(exports, "setUserRole", { enumerable: true, get: function () { return setUserRole_1.setUserRole; } });
/* ── Exports des fonctions Emprunts ── */
var createEmprunt_1 = require("./emprunts/createEmprunt");
Object.defineProperty(exports, "createEmprunt", { enumerable: true, get: function () { return createEmprunt_1.createEmprunt; } });
var generateEmpruntLabels_1 = require("./emprunts/generateEmpruntLabels");
Object.defineProperty(exports, "generateEmpruntLabels", { enumerable: true, get: function () { return generateEmpruntLabels_1.generateEmpruntLabels; } });
var updateEmpruntStatus_1 = require("./emprunts/updateEmpruntStatus");
Object.defineProperty(exports, "updateEmpruntStatus", { enumerable: true, get: function () { return updateEmpruntStatus_1.updateEmpruntStatus; } });
