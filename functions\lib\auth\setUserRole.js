"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.setUserRole = void 0;
const functions = __importStar(require("firebase-functions/v1"));
const https_1 = require("firebase-functions/v1/https");
const firebase_functions_1 = require("firebase-functions");
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const ALLOWED = new Set(["admin", "regisseur", "utilisateur"]);
const HTTPS_CODES = new Set([
    "cancelled",
    "unknown",
    "invalid-argument",
    "deadline-exceeded",
    "not-found",
    "already-exists",
    "permission-denied",
    "resource-exhausted",
    "failed-precondition",
    "aborted",
    "out-of-range",
    "unimplemented",
    "internal",
    "unavailable",
    "data-loss",
    "unauthenticated",
]);
exports.setUserRole = functions
    .region("europe-west1")
    .https.onCall(async (data, context) => {
    try {
        /* 1· Auth appelant ------------------------------------------------- */
        if (!context.auth)
            throw new https_1.HttpsError("unauthenticated", "Authentification requise.");
        if (context.auth.token.role !== "admin") {
            throw new https_1.HttpsError("permission-denied", "Seul un admin peut modifier un rôle.");
        }
        /* 2· Validation entrée ------------------------------------------- */
        const userId = String(data?.userId || "").trim();
        const role = String(data?.role || "").trim();
        if (!userId)
            throw new https_1.HttpsError("invalid-argument", "Paramètre 'userId' requis.");
        if (!ALLOWED.has(role))
            throw new https_1.HttpsError("invalid-argument", "Paramètre 'role' invalide.");
        /* 3· Claims + Firestore ------------------------------------------ */
        const auth = (0, auth_1.getAuth)();
        let retries = 2;
        while (retries--) {
            try {
                await auth.setCustomUserClaims(userId, { role });
                break;
            }
            catch (e) {
                if (e?.errorInfo?.code === "auth/internal-error" && retries) {
                    await new Promise((r) => setTimeout(r, 100)); // back-off rapide
                    continue;
                }
                throw e;
            }
        }
        await (0, firestore_1.getFirestore)()
            .collection("users")
            .doc(userId)
            .set({ role }, { merge: true });
        return { success: true, newRole: role };
    }
    catch (err) {
        firebase_functions_1.logger.error("Erreur setUserRole", JSON.stringify(err, Object.getOwnPropertyNames(err)));
        if (err instanceof https_1.HttpsError || HTTPS_CODES.has(err?.code))
            throw err;
        if (err?.errorInfo?.code === "auth/user-not-found")
            throw new https_1.HttpsError("not-found", "Utilisateur introuvable.");
        throw new https_1.HttpsError("internal", "Erreur interne du serveur");
    }
});
