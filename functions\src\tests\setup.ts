/**
 * Setup Jest : variables des émulateurs + initialisation Admin SDK
 * Chargé avant chaque suite de tests (voir jest.config.js).
 */
process.env.FIRESTORE_EMULATOR_HOST ||= "127.0.0.1:8080";
process.env.FIREBASE_AUTH_EMULATOR_HOST ||= "127.0.0.1:9099";

import { getApps, initializeApp } from "firebase-admin/app";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";

// Initialise l'app Admin (nécessaire avant getAuth()/getFirestore())
if (!getApps().length) {
  initializeApp({ projectId: "sigma-nova" });
}

// Petit smoke-check pour échouer tôt si quelque chose cloche
getAuth();
getFirestore();
