import { getApps, initializeApp } from "firebase-admin/app";

if (!getApps().length) {
  initializeApp(); // credentials auto-gérées par l’émulateur
}

/* ── Exports des Cloud Functions ── */
export { onUserCreate } from "./auth/onUserCreate";
export { setUserRole } from "./auth/setUserRole";

/* ── Exports des fonctions Emprunts ── */
export { createEmprunt } from "./emprunts/createEmprunt";
export { generateEmpruntLabels } from "./emprunts/generateEmpruntLabels";
export { updateEmpruntStatus } from "./emprunts/updateEmpruntStatus";
