import * as functions from "firebase-functions/v1";
import * as admin from "firebase-admin";
import { PDFDocument, rgb, StandardFonts } from "pdf-lib";
import { checkRegisseurOrAdmin } from "../utils/auth";

const db = admin.firestore();

interface LabelData {
  empruntId: string;
  nom: string;
  lieu: string;
  dateDepart: string;
  dateRetourPrevue: string;
  referent: string;
  emprunteur: string;
  modules: string[];
}

/**
 * Cloud Function pour générer les étiquettes PDF d'un emprunt
 * Optimisée pour respecter la contrainte de 3 secondes
 */
export const generateEmpruntLabels = functions.https.onCall(
  async (request) => {
    const startTime = Date.now();

    try {
      const data = request.data;
      const context = { auth: request.auth };

      // Vérification des permissions
      checkRegisseurOrAdmin(context);

      // Validation des données
      if (!data.empruntId || typeof data.empruntId !== "string") {
        throw new functions.https.HttpsError(
          "invalid-argument",
          "ID d'emprunt requis",
        );
      }

      // Récupération des données de l'emprunt
      const labelData = await getEmpruntLabelData(data.empruntId);

      // Génération du PDF
      const pdfBytes = await generatePDF(labelData);

      // Conversion en base64 pour le retour
      const pdfBase64 = Buffer.from(pdfBytes).toString("base64");

      const endTime = Date.now();
      const duration = endTime - startTime;

      functions.logger.info(`Étiquettes PDF générées: ${data.empruntId}`, {
        empruntId: data.empruntId,
        duration: `${duration}ms`,
        generatedBy: context.auth!.uid,
      });

      // Vérification de la contrainte de performance
      if (duration > 3000) {
        functions.logger.warn(
          `Génération PDF lente: ${duration}ms pour ${data.empruntId}`,
        );
      }

      return {
        success: true,
        pdf: pdfBase64,
        filename: `etiquettes_emprunt_${data.empruntId}.pdf`,
        duration,
      };
    } catch (error) {
      functions.logger.error("Erreur lors de la génération PDF:", error);

      if (error instanceof functions.https.HttpsError) {
        throw error;
      }

      throw new functions.https.HttpsError(
        "internal",
        "Erreur interne lors de la génération du PDF",
      );
    }
  },
);

/**
 * Récupère les données nécessaires pour les étiquettes
 */
async function getEmpruntLabelData(empruntId: string): Promise<LabelData> {
  const empruntRef = db.collection("emprunts").doc(empruntId);
  const empruntDoc = await empruntRef.get();

  if (!empruntDoc.exists) {
    throw new functions.https.HttpsError("not-found", "Emprunt non trouvé");
  }

  const empruntData = empruntDoc.data()!;

  // Récupération des modules associés - Optimisé pour éviter N+1
  const materielSnapshot = await empruntRef.collection("materiel").get();
  const modules: string[] = [];

  // Collecter toutes les références de documents à lire
  const moduleRefs: admin.firestore.DocumentReference[] = [];
  const stockRefs: admin.firestore.DocumentReference[] = [];
  const materielDataMap = new Map<string, any>();

  for (const materielDoc of materielSnapshot.docs) {
    const materielData = materielDoc.data();
    materielDataMap.set(materielData.idMateriel, materielData);

    if (materielData.type === "module") {
      moduleRefs.push(db.collection("modules").doc(materielData.idMateriel));
    } else if (materielData.type === "stock") {
      stockRefs.push(db.collection("stocks").doc(materielData.idMateriel));
    }
  }

  // Lecture groupée des modules
  if (moduleRefs.length > 0) {
    const moduleDocs = await db.getAll(...moduleRefs);
    for (const moduleDoc of moduleDocs) {
      if (moduleDoc.exists) {
        modules.push(moduleDoc.data()!.nom);
      }
    }
  }

  // Lecture groupée des stocks
  if (stockRefs.length > 0) {
    const stockDocs = await db.getAll(...stockRefs);
    for (const stockDoc of stockDocs) {
      if (stockDoc.exists) {
        const materielData = materielDataMap.get(stockDoc.id);
        modules.push(`${stockDoc.data()!.nom} (x${materielData.quantite})`);
      }
    }
  }

  return {
    empruntId,
    nom: empruntData.nom,
    lieu: empruntData.lieu,
    dateDepart: empruntData.dateDepart.toDate().toLocaleDateString("fr-FR"),
    dateRetourPrevue: empruntData.dateRetourPrevue
      .toDate()
      .toLocaleDateString("fr-FR"),
    referent: empruntData.referent,
    emprunteur: empruntData.emprunteur,
    modules,
  };
}

/**
 * Génère le PDF des étiquettes
 */
async function generatePDF(labelData: LabelData): Promise<Uint8Array> {
  // Création du document PDF
  const pdfDoc = await PDFDocument.create();

  // Configuration de la police
  const font = await pdfDoc.embedFont(StandardFonts.Helvetica);
  const boldFont = await pdfDoc.embedFont(StandardFonts.HelveticaBold);

  // Dimensions d'une étiquette (format A4 divisé en 4)
  const labelWidth = 297; // A4 width in mm converted to points
  const labelHeight = 210; // A4 height in mm converted to points

  // Création de la page
  const page = pdfDoc.addPage([labelWidth, labelHeight]);

  // Couleurs
  const blackColor = rgb(0, 0, 0);
  const grayColor = rgb(0.5, 0.5, 0.5);

  // Position de départ
  let yPosition = labelHeight - 40;
  const leftMargin = 20;
  const lineHeight = 20;

  // Titre principal
  page.drawText("ÉTIQUETTE D'EMPRUNT SIGMA", {
    x: leftMargin,
    y: yPosition,
    size: 16,
    font: boldFont,
    color: blackColor,
  });

  yPosition -= lineHeight * 1.5;

  // Numéro d'emprunt
  page.drawText(`N° Emprunt: ${labelData.empruntId}`, {
    x: leftMargin,
    y: yPosition,
    size: 12,
    font: boldFont,
    color: blackColor,
  });

  yPosition -= lineHeight;

  // Informations principales
  const mainInfo = [
    { label: "Manipulation:", value: labelData.nom },
    { label: "Lieu:", value: labelData.lieu },
    { label: "Départ:", value: labelData.dateDepart },
    { label: "Retour prévu:", value: labelData.dateRetourPrevue },
    { label: "Référent:", value: labelData.referent },
    { label: "Emprunteur:", value: labelData.emprunteur },
  ];

  for (const info of mainInfo) {
    page.drawText(info.label, {
      x: leftMargin,
      y: yPosition,
      size: 10,
      font: boldFont,
      color: blackColor,
    });

    page.drawText(info.value, {
      x: leftMargin + 80,
      y: yPosition,
      size: 10,
      font: font,
      color: blackColor,
    });

    yPosition -= lineHeight * 0.8;
  }

  yPosition -= lineHeight * 0.5;

  // Section modules
  page.drawText("MODULES/MATÉRIEL:", {
    x: leftMargin,
    y: yPosition,
    size: 11,
    font: boldFont,
    color: blackColor,
  });

  yPosition -= lineHeight;

  // Liste des modules
  if (labelData.modules.length > 0) {
    for (const module of labelData.modules) {
      page.drawText(`• ${module}`, {
        x: leftMargin + 10,
        y: yPosition,
        size: 9,
        font: font,
        color: blackColor,
      });

      yPosition -= lineHeight * 0.7;
    }
  } else {
    page.drawText("Aucun module associé", {
      x: leftMargin + 10,
      y: yPosition,
      size: 9,
      font: font,
      color: grayColor,
    });
  }

  // Ligne de séparation
  yPosition -= lineHeight;
  page.drawLine({
    start: { x: leftMargin, y: yPosition },
    end: { x: labelWidth - leftMargin, y: yPosition },
    thickness: 1,
    color: grayColor,
  });

  yPosition -= lineHeight;

  // Instructions
  const instructions = [
    "INSTRUCTIONS:",
    "1. Vérifier le contenu avant départ",
    "2. Signaler tout problème au régisseur",
    "3. Respecter la date de retour",
    "4. Retourner complet et en bon état",
  ];

  for (let i = 0; i < instructions.length; i++) {
    const instruction = instructions[i];
    const isTitle = i === 0;

    page.drawText(instruction, {
      x: leftMargin,
      y: yPosition,
      size: isTitle ? 10 : 8,
      font: isTitle ? boldFont : font,
      color: isTitle ? blackColor : grayColor,
    });

    yPosition -= lineHeight * (isTitle ? 1 : 0.7);
  }

  // Pied de page
  page.drawText(
    `Généré le ${new Date().toLocaleDateString("fr-FR")} à ${new Date().toLocaleTimeString("fr-FR")}`,
    {
      x: leftMargin,
      y: 20,
      size: 7,
      font: font,
      color: grayColor,
    },
  );

  // Sérialisation du PDF
  return await pdfDoc.save();
}
