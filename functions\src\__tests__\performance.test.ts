// 1. Initialisation de l'environnement de test
import ftest from 'firebase-functions-test';
import * as admin from 'firebase-admin';

// 2. Mocking de firebase-admin AVANT l'import de la fonction
// Cela intercepte tous les appels à admin.initializeApp(), firestore(), etc.
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    runTransaction: jest.fn(callback => callback({
      get: jest.fn().mockResolvedValue({ exists: true }),
      set: jest.fn(),
      update: jest.fn(),
    })),
  })),
}));

// 3. Import des fonctions APRÈS le mocking
import { generateEmpruntLabels } from '../emprunts/generateEmpruntLabels';
import { createEmprunt } from '../emprunts/createEmprunt';
import { updateEmpruntStatus } from '../emprunts/updateEmpruntStatus';

// Initialisation de l'environnement de test avec un projet hors ligne
const testEnv = ftest({
  projectId: 'sigma-agent-test',
});

describe('Performance Tests', () => {

  afterAll(() => {
    testEnv.cleanup(); // Nettoyage après les tests
  });

  it('should test generateEmpruntLabels performance', async () => {
    // 4. Wrapper la fonction pour la rendre appelable dans les tests
    const wrapped = testEnv.wrap(generateEmpruntLabels);

    const request = {
      data: { empruntId: 'test-emprunt-id' },
      auth: {
        uid: 'test-uid',
        token: { role: 'regisseur' },
      },
    };

    // 5. Appeler la fonction wrappée
    const result = await wrapped(request);

    expect(result).toHaveProperty('success', true);
  });

  it('should test createEmprunt performance', async () => {
    const wrapped = testEnv.wrap(createEmprunt);

    const request = {
      data: {
        nom: 'Test Performance',
        lieu: 'Studio A',
        dateDepart: new Date().toISOString(),
        dateRetour: new Date(Date.now() + 86400000).toISOString(),
        secteur: 'Production',
        referent: 'John Doe',
        emprunteur: 'Jane Doe',
        materiel: [],
      },
      auth: {
        uid: 'test-uid',
        token: { role: 'regisseur' },
      },
    };

    const result = await wrapped(request);

    expect(result).toHaveProperty('success', true);
  });

  it('should test updateEmpruntStatus performance', async () => {
    const wrapped = testEnv.wrap(updateEmpruntStatus);

    const request = {
      data: {
        empruntId: 'test-emprunt-id',
        newStatus: 'Prêt',
      },
      auth: {
        uid: 'test-uid',
        token: { role: 'regisseur' },
      },
    };

    const result = await wrapped(request);

    expect(result).toHaveProperty('success', true);
  });

  // ... Ajoutez ici les autres cas de test (performance, concurrence, etc.)
});
