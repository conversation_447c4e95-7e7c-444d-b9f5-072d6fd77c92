import * as functions from "firebase-functions/v1";
import { HttpsError } from "firebase-functions/v1/https";
import { logger } from "firebase-functions";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";

type Role = "admin" | "regisseur" | "utilisateur";
const ALLOWED: Set<Role> = new Set(["admin", "regisseur", "utilisateur"]);
const HTTPS_CODES = new Set([
  "cancelled",
  "unknown",
  "invalid-argument",
  "deadline-exceeded",
  "not-found",
  "already-exists",
  "permission-denied",
  "resource-exhausted",
  "failed-precondition",
  "aborted",
  "out-of-range",
  "unimplemented",
  "internal",
  "unavailable",
  "data-loss",
  "unauthenticated",
]);

export const setUserRole = functions
  .region("europe-west1")
  .https.onCall(async (data, context) => {
    try {
      /* 1· Auth appelant ------------------------------------------------- */
      if (!context.auth)
        throw new HttpsError("unauthenticated", "Authentification requise.");
      if (context.auth.token.role !== "admin") {
        throw new HttpsError(
          "permission-denied",
          "Seul un admin peut modifier un rôle.",
        );
      }

      /* 2· Validation entrée ------------------------------------------- */
      const userId = String(data?.userId || "").trim();
      const role = String(data?.role || "").trim() as Role;
      if (!userId)
        throw new HttpsError("invalid-argument", "Paramètre 'userId' requis.");
      if (!ALLOWED.has(role))
        throw new HttpsError("invalid-argument", "Paramètre 'role' invalide.");

      /* 3· Claims + Firestore ------------------------------------------ */
      const auth = getAuth();
      let retries = 2;
      while (retries--) {
        try {
          await auth.setCustomUserClaims(userId, { role });
          break;
        } catch (e: any) {
          if (e?.errorInfo?.code === "auth/internal-error" && retries) {
            await new Promise((r) => setTimeout(r, 100)); // back-off rapide
            continue;
          }
          throw e;
        }
      }

      await getFirestore()
        .collection("users")
        .doc(userId)
        .set({ role }, { merge: true });
      return { success: true, newRole: role };
    } catch (err: any) {
      logger.error(
        "Erreur setUserRole",
        JSON.stringify(err, Object.getOwnPropertyNames(err)),
      );
      if (err instanceof HttpsError || HTTPS_CODES.has(err?.code)) throw err;
      if (err?.errorInfo?.code === "auth/user-not-found")
        throw new HttpsError("not-found", "Utilisateur introuvable.");
      throw new HttpsError("internal", "Erreur interne du serveur");
    }
  });
