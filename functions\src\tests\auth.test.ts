// 1. Initialisation de l'environnement de test
import ftest from 'firebase-functions-test';
import * as admin from 'firebase-admin';

// 2. Mocking de firebase-admin AVANT l'import de la fonction
// Cela intercepte tous les appels à admin.initializeApp(), firestore(), etc.
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    runTransaction: jest.fn(callback => callback({
      get: jest.fn().mockResolvedValue({ exists: true }),
      set: jest.fn(),
      update: jest.fn(),
    })),
  })),
}));

// 3. Import des fonctions APRÈS le mocking
import { setUserRole } from '../auth/setUserRole';
import { onUserCreate } from '../auth/onUserCreate';

// Initialisation de l'environnement de test avec un projet hors ligne
const testEnv = ftest({
  projectId: 'sigma-agent-test',
});

describe('Cloud Functions d\'Authentification', () => {

  afterAll(() => {
    testEnv.cleanup(); // Nettoyage après les tests
  });

  it('devrait permettre à un admin d\'assigner un rôle', async () => {
    // 4. Wrapper la fonction pour la rendre appelable dans les tests
    const wrapped = testEnv.wrap(setUserRole);

    const data = {
      userId: 'test-user-uid',
      role: 'regisseur',
    };

    const context = {
      auth: {
        uid: 'test-admin-uid',
        token: { role: 'admin' },
      },
    };

    // 5. Appeler la fonction wrappée (ancienne signature avec data, context séparés)
    const result = await wrapped(data, context);

    expect(result).toHaveProperty('success', true);
    expect(result).toHaveProperty('newRole', 'regisseur');
  });

  it('devrait retourner une erreur de permission si l\'utilisateur n\'est pas admin', async () => {
    const wrapped = testEnv.wrap(setUserRole);
    const data = {
      userId: 'test-user-uid',
      role: 'regisseur',
    };
    const context = {
      auth: {
        uid: 'user-uid',
        token: { role: 'utilisateur' }, // Rôle insuffisant
      },
    };

    // Utiliser `expect.assertions` pour s'assurer que l'erreur est bien levée
    expect.assertions(2);
    try {
      await wrapped(data, context);
    } catch (error: any) {
      expect(error.code).toBe('permission-denied');
      expect(error.message).toBe('Seul un admin peut modifier un rôle.');
    }
  });

  // ... Ajoutez ici les autres cas de test (onUserCreate, etc.)
});
