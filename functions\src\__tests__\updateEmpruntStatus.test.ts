// 1. Initialisation de l'environnement de test
import ftest from 'firebase-functions-test';
import * as admin from 'firebase-admin';

// 2. Mocking de firebase-admin AVANT l'import de la fonction
// Cela intercepte tous les appels à admin.initializeApp(), firestore(), etc.
jest.mock('firebase-admin', () => ({
  initializeApp: jest.fn(),
  firestore: jest.fn(() => ({
    collection: jest.fn().mockReturnThis(),
    doc: jest.fn().mockReturnThis(),
    runTransaction: jest.fn(callback => callback({
      get: jest.fn().mockResolvedValue({ exists: true }),
      set: jest.fn(),
      update: jest.fn(),
    })),
  })),
}));

// 3. Import de la fonction APRÈS le mocking
import { updateEmpruntStatus } from '../emprunts/updateEmpruntStatus';

// Initialisation de l'environnement de test avec un projet hors ligne
const testEnv = ftest({
  projectId: 'sigma-agent-test',
});

describe('updateEmpruntStatus Cloud Function', () => {

  afterAll(() => {
    testEnv.cleanup(); // Nettoyage après les tests
  });

  it('devrait permettre la transition de \'Pas prêt\' vers \'Prêt\'', async () => {
    // 4. Wrapper la fonction pour la rendre appelable dans les tests
    const wrapped = testEnv.wrap(updateEmpruntStatus);

    const request = {
      data: {
        empruntId: 'test-emprunt-id',
        newStatus: 'Prêt',
        notes: 'Test transition',
      },
      auth: {
        uid: 'test-uid',
        token: { role: 'regisseur' },
      },
    };

    // 5. Appeler la fonction wrappée
    const result = await wrapped(request);

    expect(result).toHaveProperty('success', true);
    expect(result).toHaveProperty('result');
  });

  it('devrait retourner une erreur de permission si l\'utilisateur n\'est pas régisseur ou admin', async () => {
    const wrapped = testEnv.wrap(updateEmpruntStatus);
    const request = {
      data: {
        empruntId: 'test-emprunt-id',
        newStatus: 'Prêt',
      },
      auth: {
        uid: 'user-uid',
        token: { role: 'utilisateur' }, // Rôle insuffisant
      },
    };

    // Utiliser `expect.assertions` pour s'assurer que l'erreur est bien levée
    expect.assertions(2);
    try {
      await wrapped(request);
    } catch (error: any) {
      expect(error.code).toBe('permission-denied');
      expect(error.message).toBe('Accès refusé. Rôles requis: regisseur, admin');
    }
  });

  // ... Ajoutez ici les autres cas de test (transitions, validation, etc.)
});
