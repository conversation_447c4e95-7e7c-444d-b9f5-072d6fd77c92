# Briefing de Mission FINALE : E-2-Tests-Correction-Mocks

## 1\. OBJECTIF STRATÉGIQUE

Atteindre 100% de succès sur la suite de tests unitaires en corrigeant les mocks Firestore et les données de test invalides, en suivant un modèle de code précis.

## 2\. CONTEXTE

* Tu es sur la branche `feature/E2-flux-emprunts-correction`.
* Ton application du template de test a échoué car les mocks de base de données étaient incomplets.
* **Leçon Clé :** Un mock doit simuler la structure de données complète que le code testé s'attend à recevoir.
* Je te fournis ci-dessous le **modèle de code exact** que tu dois appliquer. C'est ta seule source de vérité pour cette mission.

## 3\. CRITÈRE DE SUCCÈS UNIQUE ET NON-NÉGOCIABLE

* \[ ] La commande `npm run test:ci` dans le dossier `functions` se termine avec **0 échecs**.

## 4\. MODÈLE DE CORRECTION DE MOCK (À APPLIQUER PARTOUT)

Voici comment corriger un mock Firestore pour simuler la lecture d'un document. C'est le **seul** pattern que tu dois suivre.

**Exemple de référence pour `updateEmpruntStatus.test.ts` :**

```typescript
// DANS ton bloc jest.mock('firebase-admin', ...)

// ...
firestore: jest.fn(() => ({
  collection: jest.fn().mockReturnThis(),
  // Le mock pour .doc() doit retourner un objet qui a les fonctions .get() et .data()
  doc: jest.fn().mockReturnValue({
    get: jest.fn().mockResolvedValue({
      exists: true,
      data: () => ({ // La fonction .data() DOIT retourner les données du document simulé
        nom: 'Emprunt Existant',
        status: 'Prêt',
        // ... autres champs nécessaires pour la validation
      })
    })
  }),
  runTransaction: jest.fn(callback => {
    // Le mock pour la transaction doit aussi simuler la lecture
    const transaction = {
      get: jest.fn().mockResolvedValue({
        exists: true,
        data: () => ({ nom: 'Module A', quantite: 10 })
      }),
      update: jest.fn(),
      set: jest.fn()
    };
    return callback(transaction);
  })
}))
// ...

