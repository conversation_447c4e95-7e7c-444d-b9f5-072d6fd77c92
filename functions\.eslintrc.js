module.exports = {
  root: true,
  env: { es6: true, node: true, jest: true },
  parser: '@typescript-eslint/parser',
  parserOptions: {
    project: ['tsconfig.json', 'tsconfig.dev.json'],
    sourceType: 'module',
    // Évite l’avertissement "unsupported TS version"
    warnOnUnsupportedTypeScriptVersion: false,
  },
  plugins: ['@typescript-eslint', 'import', 'prettier'],
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:import/errors',
    'plugin:import/warnings',
    'plugin:import/typescript',
    // Fait passer Prettier en autorité sur le formatage
    'plugin:prettier/recommended',
  ],
  rules: {
    'prettier/prettier': ['error'],
    'quotes': ['error', 'double'],
    'import/no-unresolved': 'off',

    // On coupe le bruit qui te bloque:
    'max-len': 'off',
    'require-jsdoc': 'off',
    'valid-jsdoc': 'off',
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-non-null-assertion': 'warn',
  },
  ignorePatterns: ['/lib/**/*', '/generated/**/*'],
};
