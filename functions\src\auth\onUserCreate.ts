import * as functions from "firebase-functions/v1";
import { getAuth } from "firebase-admin/auth";
import { getFirestore } from "firebase-admin/firestore";

/**
 * Assigne le rôle "utilisateur" au nouvel utilisateur
 * et crée/merge le doc /users/{uid}.
 */
export const onUserCreate = functions
  .region("europe-west1")
  .auth.user()
  .onCreate(async (user: functions.auth.UserRecord) => {
    const auth = getAuth();
    const db = getFirestore();

    const role = "utilisateur" as const;

    // Claims custom du nouvel utilisateur
    await auth.setCustomUserClaims(user.uid, { role });

    // Doc utilisateur
    await db
      .collection("users")
      .doc(user.uid)
      .set(
        {
          email: user.email ?? null,
          role,
          createdAt: new Date().toISOString(),
        },
        { merge: true },
      );
  });
