# Nom du workflow qui apparaîtra dans l'onglet "Actions" de GitHub
name: SIGMA CI - Build, Lint & Test

# Déclencheur : ce workflow se lancera à chaque Pull Request vers la branche `main`
on:
  pull_request:
    branches: [ main ]
  # Permet aussi de le lancer manuellement depuis l'interface GitHub
  workflow_dispatch:

# Définition des tâches (jobs) à exécuter
jobs:
  build-and-test:
    # L'environnement sur lequel le job va tourner (une machine virtuelle Ubuntu)
    runs-on: ubuntu-latest

    # Les étapes du job
    steps:
      # Étape 1 : Récupérer le code de votre dépôt
      - name: Checkout repository
        uses: actions/checkout@v4

      # Étape 2 : Configurer Node.js (version 20 comme dans votre package.json)
      - name: Setup Node.js v20
        uses: actions/setup-node@v4
        with:
          node-version: 20
          cache: 'npm'
          cache-dependency-path: 'functions/package-lock.json' 

      # Étape 3 : Installer les dépendances de manière robuste
      - name: Install Dependencies
        working-directory: ./src/firebase/functions
        run: npm ci

      # Étape 4 : Linter le code
      - name: Run Linter
        working-directory: ./src/firebase/functions
        run: npm run lint

      # Étape 5 : Lancer les tests unitaires
      - name: Run Unit Tests
        working-directory: ./src/firebase/functions
        run: npm run test:ci

      # Étape 6 : Compiler le code TypeScript
      - name: Build TypeScript
        working-directory: ./src/firebase/functions
        run: npm run build