import { test, expect } from '@playwright/test';

/**
 * Tests E2E pour la création d'emprunt - Migration de Cypress vers Playwright
 * Valide le scénario complet de création d'emprunt avec attentes sur requêtes réseau
 */

test.describe('Création d\'emprunt - Formulaire multi-étapes', () => {
  test.beforeEach(async ({ page }) => {
    // Navigation vers la page de création d'emprunt
    await page.goto('/emprunts/nouveau');
    
    // Mock de l'authentification utilisateur
    await page.evaluate(() => {
      window.localStorage.setItem('user', JSON.stringify({
        uid: 'test-user',
        role: 'regisseur',
        email: '<EMAIL>'
      }));
    });
  });

  test('devrait afficher le formulaire de création d\'emprunt', async ({ page }) => {
    // Vérifier que le formulaire est visible
    await expect(page.locator('[data-cy=emprunt-form]')).toBeVisible();
    await expect(page.locator('[data-cy=step-indicator]')).toContainText('Étape 1 sur 4');
  });

  test('devrait naviguer à travers toutes les étapes du wizard', async ({ page }) => {
    // Étape 1: Informations de base
    await page.fill('[data-cy=nom-manipulation]', 'Test Manipulation Playwright');
    await page.fill('[data-cy=lieu]', 'Salle de test');
    await page.fill('[data-cy=date-depart]', '2024-12-01');
    await page.fill('[data-cy=date-retour]', '2024-12-05');
    await page.selectOption('[data-cy=secteur]', 'Test Secteur');
    await page.fill('[data-cy=referent]', 'Test Référent');
    await page.fill('[data-cy=emprunteur]', 'Test Emprunteur');
    
    await page.click('[data-cy=next-step]');

    // Vérifier la transition vers l'étape 2
    await expect(page.locator('[data-cy=step-indicator]')).toContainText('Étape 2 sur 4');
    await expect(page.locator('[data-cy=materiel-selection]')).toBeVisible();

    // Étape 2: Sélection du matériel
    await page.fill('[data-cy=materiel-search]', 'module test');
    await page.click('[data-cy=materiel-item]');
    await page.click('[data-cy=add-materiel]');
    await page.click('[data-cy=next-step]');

    // Vérifier la transition vers l'étape 3
    await expect(page.locator('[data-cy=step-indicator]')).toContainText('Étape 3 sur 4');
    await expect(page.locator('[data-cy=livraison-options]')).toBeVisible();

    // Étape 3: Options de livraison (passer)
    await page.click('[data-cy=skip-livraison]');

    // Vérifier la transition vers l'étape 4
    await expect(page.locator('[data-cy=step-indicator]')).toContainText('Étape 4 sur 4');
    await expect(page.locator('[data-cy=validation-summary]')).toBeVisible();
  });

  test('devrait valider les champs requis', async ({ page }) => {
    // Essayer de passer à l'étape suivante sans remplir les champs
    await page.click('[data-cy=next-step]');
    
    // Vérifier que les erreurs sont affichées
    await expect(page.locator('[data-cy=error-nom]')).toBeVisible();
    await expect(page.locator('[data-cy=error-lieu]')).toBeVisible();
    await expect(page.locator('[data-cy=error-date-depart]')).toBeVisible();
    await expect(page.locator('[data-cy=error-date-retour]')).toBeVisible();
  });

  test('devrait valider les dates', async ({ page }) => {
    await page.fill('[data-cy=nom-manipulation]', 'Test Manipulation');
    await page.fill('[data-cy=lieu]', 'Salle de test');
    await page.fill('[data-cy=date-depart]', '2024-12-05');
    await page.fill('[data-cy=date-retour]', '2024-12-01'); // Date antérieure
    await page.selectOption('[data-cy=secteur]', 'Test Secteur');
    await page.fill('[data-cy=referent]', 'Test Référent');
    await page.fill('[data-cy=emprunteur]', 'Test Emprunteur');
    
    await page.click('[data-cy=next-step]');
    
    // Vérifier l'erreur de validation des dates
    await expect(page.locator('[data-cy=error-dates]')).toContainText('La date de retour doit être postérieure');
  });

  test('devrait créer l\'emprunt avec succès et attendre la requête réseau', async ({ page }) => {
    // Intercepter la requête de création d'emprunt
    const createEmpruntPromise = page.waitForRequest(request => 
      request.url().includes('createEmprunt') && request.method() === 'POST'
    );

    // Intercepter la réponse de création d'emprunt
    const createEmpruntResponsePromise = page.waitForResponse(response => 
      response.url().includes('createEmprunt') && response.status() === 200
    );

    // Remplir le formulaire complet
    await page.fill('[data-cy=nom-manipulation]', 'Test Manipulation Complète');
    await page.fill('[data-cy=lieu]', 'Salle de test complète');
    await page.fill('[data-cy=date-depart]', '2024-12-01');
    await page.fill('[data-cy=date-retour]', '2024-12-05');
    await page.selectOption('[data-cy=secteur]', 'Test Secteur');
    await page.fill('[data-cy=referent]', 'Test Référent');
    await page.fill('[data-cy=emprunteur]', 'Test Emprunteur');
    await page.click('[data-cy=next-step]');

    // Étape 2: Ajouter du matériel
    await page.fill('[data-cy=materiel-search]', 'module test');
    await page.click('[data-cy=materiel-item]');
    await page.click('[data-cy=add-materiel]');
    await page.click('[data-cy=next-step]');

    // Étape 3: Passer la livraison
    await page.click('[data-cy=skip-livraison]');

    // Étape 4: Valider la création
    await page.click('[data-cy=create-emprunt]');

    // Attendre la requête et la réponse réseau (remplace les wait fixes)
    const request = await createEmpruntPromise;
    const response = await createEmpruntResponsePromise;

    // Vérifier que la requête a été envoyée avec les bonnes données
    expect(request.method()).toBe('POST');
    const requestData = request.postDataJSON();
    expect(requestData.nom).toBe('Test Manipulation Complète');
    expect(requestData.lieu).toBe('Salle de test complète');

    // Vérifier que la réponse est positive
    expect(response.status()).toBe(200);
    const responseData = await response.json();
    expect(responseData.success).toBe(true);

    // Vérifier le message de succès
    await expect(page.locator('[data-cy=success-message]')).toBeVisible();
    await expect(page.locator('[data-cy=success-message]')).toContainText('Emprunt créé avec succès');
    
    // Vérifier la redirection
    await expect(page).toHaveURL(/\/emprunts\//);
  });

  test('devrait afficher les erreurs de création avec gestion réseau', async ({ page }) => {
    // Mock d'une erreur de création via interception réseau
    await page.route('**/createEmprunt', route => {
      route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({ error: 'Erreur de création' })
      });
    });

    // Remplir et soumettre le formulaire
    await page.fill('[data-cy=nom-manipulation]', 'Test Erreur');
    await page.fill('[data-cy=lieu]', 'Salle de test');
    await page.fill('[data-cy=date-depart]', '2024-12-01');
    await page.fill('[data-cy=date-retour]', '2024-12-05');
    await page.selectOption('[data-cy=secteur]', 'Test Secteur');
    await page.fill('[data-cy=referent]', 'Test Référent');
    await page.fill('[data-cy=emprunteur]', 'Test Emprunteur');
    await page.click('[data-cy=next-step]');
    await page.click('[data-cy=skip-livraison]');
    await page.click('[data-cy=skip-livraison]');
    await page.click('[data-cy=create-emprunt]');

    // Attendre la réponse d'erreur
    const errorResponse = await page.waitForResponse(response => 
      response.url().includes('createEmprunt') && response.status() === 400
    );

    expect(errorResponse.status()).toBe(400);

    // Vérifier l'affichage de l'erreur
    await expect(page.locator('[data-cy=error-message]')).toBeVisible();
    await expect(page.locator('[data-cy=error-message]')).toContainText('Erreur de création');
  });

  test('devrait gérer les permissions utilisateur', async ({ page }) => {
    // Mock d'un utilisateur sans permissions
    await page.evaluate(() => {
      window.localStorage.setItem('user', JSON.stringify({
        uid: 'test-user-no-perm',
        role: 'utilisateur', // Pas régisseur
        email: '<EMAIL>'
      }));
    });

    // Recharger la page pour prendre en compte les nouvelles permissions
    await page.reload();

    // Vérifier que l'accès est refusé
    await expect(page.locator('[data-cy=access-denied]')).toBeVisible();
    await expect(page.locator('[data-cy=access-denied]')).toContainText('Accès refusé');
  });

  test('devrait sauvegarder automatiquement le brouillon', async ({ page }) => {
    // Intercepter les requêtes de sauvegarde automatique
    const autosavePromise = page.waitForRequest(request => 
      request.url().includes('saveDraft') && request.method() === 'POST'
    );

    // Remplir partiellement le formulaire
    await page.fill('[data-cy=nom-manipulation]', 'Brouillon Test');
    await page.fill('[data-cy=lieu]', 'Salle brouillon');

    // Attendre la sauvegarde automatique (debounced)
    await page.waitForTimeout(2000);

    // Vérifier que la sauvegarde a été déclenchée
    const autosaveRequest = await autosavePromise;
    expect(autosaveRequest.method()).toBe('POST');
    
    const requestData = autosaveRequest.postDataJSON();
    expect(requestData.status).toBe('draft');
    expect(requestData.nom).toBe('Brouillon Test');

    // Vérifier l'indicateur de sauvegarde
    await expect(page.locator('[data-cy=autosave-indicator]')).toContainText('Brouillon sauvegardé');
  });

  test('devrait valider la disponibilité du matériel en temps réel', async ({ page }) => {
    // Mock de la vérification de disponibilité
    await page.route('**/checkMaterielAvailability', route => {
      route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({ 
          available: false, 
          reason: 'Module déjà emprunté' 
        })
      });
    });

    // Naviguer jusqu'à l'étape de sélection du matériel
    await page.fill('[data-cy=nom-manipulation]', 'Test Disponibilité');
    await page.fill('[data-cy=lieu]', 'Salle de test');
    await page.fill('[data-cy=date-depart]', '2024-12-01');
    await page.fill('[data-cy=date-retour]', '2024-12-05');
    await page.selectOption('[data-cy=secteur]', 'Test Secteur');
    await page.fill('[data-cy=referent]', 'Test Référent');
    await page.fill('[data-cy=emprunteur]', 'Test Emprunteur');
    await page.click('[data-cy=next-step]');

    // Rechercher un module
    await page.fill('[data-cy=materiel-search]', 'module indisponible');
    
    // Attendre la vérification de disponibilité
    await page.waitForResponse(response => 
      response.url().includes('checkMaterielAvailability')
    );

    // Vérifier l'affichage de l'indisponibilité
    await expect(page.locator('[data-cy=materiel-unavailable]')).toBeVisible();
    await expect(page.locator('[data-cy=materiel-unavailable]')).toContainText('Module déjà emprunté');
  });
});
